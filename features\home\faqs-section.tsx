import { ArrowRightIcon } from 'lucide-react';
import Link from 'next/link';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { faqs } from '@/lib/docs';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from './page-header';

export function FaqsSection() {
  return (
    <section className="border-grid">
      <PageHeader className="py-20">
        <PageHeaderHeading className="font-mono text-3xl lg:text-4xl">
          Frequently Asked Questions
        </PageHeaderHeading>
        <PageHeaderDescription className=" text-foreground/80 tracking-tight">
          We&apos;re here to help. Any more questions feel free to ask, please
          don&apos;t hesitate to reach out.
        </PageHeaderDescription>
        <Badge asChild className="rounded-full" variant="secondary">
          <Link href="/contact">
            Ask a Question <ArrowRightIcon />
          </Link>
        </Badge>
        <div className="mx-auto w-full max-w-3xl text-left">
          <Accordion className="w-full" collapsible type="single">
            {faqs.map((faq) => (
              <AccordionItem key={faq.question} value={faq.question}>
                <AccordionTrigger className="cursor-pointer font-mono text-base text-foreground/80 md:text-lg">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-lg text-muted-foreground">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </PageHeader>
    </section>
  );
}
