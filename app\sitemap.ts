import { allBlogs } from "content-collections";
import type { MetadataRoute } from "next";
import { siteConfig } from "@/lib/config";
export default function sitemap(): MetadataRoute.Sitemap {
  return [
    {
      url: siteConfig.url,
      lastModified: new Date().toISOString(),
    },
    {
      url: `${siteConfig.url}/about`,
    },
    {
      url: `${siteConfig.url}/blogs`,
    },
    {
      url: `${siteConfig.url}/contact`,
    },
    {
      url: `${siteConfig.url}/book`,
    },
    {
      url: `${siteConfig.url}/privacy`,
    },
    {
      url: `${siteConfig.url}/marketplace`,
    },
    {
      url: `${siteConfig.url}/terms`,
    },
    ...allBlogs.map((post) => ({
      url: `${siteConfig.url}${post.slug}`,
      lastModified: post.publishedOn,
    })),
  ];
}
