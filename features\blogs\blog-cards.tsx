import type { Blog } from 'content-collections';
import { motion } from 'motion/react';
import { InView } from '@/components/ui/in-view';
import { BlogCard } from './blog-card';
export function BlogCards({ blogs }: { blogs: Blog[] }) {
  return (
    <InView
      variants={{
        hidden: {
          opacity: 0,
        },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: 0.09,
          },
        },
      }}
      viewOptions={{ once: false, margin: '0px 0px -250px 0px' }}
    >
      <div className="relative grid grid-cols-1 gap-5 overflow-hidden md:grid-cols-2 lg:grid-cols-3">
        {blogs.map((blog: Blog) => (
          <motion.div
            key={blog.title}
            variants={{
              hidden: { opacity: 0, scale: 0.8, filter: 'blur(10px)' },
              visible: {
                opacity: 1,
                scale: 1,
                filter: 'blur(0px)',
              },
            }}
          >
            <BlogCard blog={blog} key={blog._meta.path} />
          </motion.div>
        ))}
      </div>
    </InView>
  );
}
