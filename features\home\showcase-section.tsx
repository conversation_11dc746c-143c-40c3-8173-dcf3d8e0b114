import { allShowcases } from 'content-collections';
import { ChevronRightIcon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { Marquee } from '@/components/ui/marquee';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from './page-header';

export interface ShowcaseCardProps {
  title: string;
  image: string;
  href: string;
  affiliation?: string;
}

export function ShowcaseSection() {
  return (
    <section className="overflow-x-hidden border-grid">
      <PageHeader className="lg:pt-6">
        <PageHeaderHeading className="font-mono text-3xl lg:text-4xl">
          Our Work, Your Edge
        </PageHeaderHeading>
        <PageHeaderDescription className=" text-foreground/80 tracking-tight">
          Rathon powers beautiful platforms for businesses that value clarity,
          speed, and impact.
        </PageHeaderDescription>

        <div className="relative flex flex-col overflow-x-hidden">
          <Marquee className="max-w-screen [--duration:40s]">
            {allShowcases
              .filter((showcase) => showcase.featured)
              .map((showcase) => (
                <ShowcaseCard
                  key={showcase.title}
                  {...showcase}
                  href={showcase.href} // Use href directly instead of slug
                />
              ))}
          </Marquee>
          <div className="pointer-events-none absolute inset-y-0 left-0 h-full w-[5%] bg-gradient-to-r from-background" />
          <div className="pointer-events-none absolute inset-y-0 right-0 h-full w-[5%] bg-gradient-to-l from-background" />
        </div>
      </PageHeader>
    </section>
  );
}

export function ShowcaseCard({
  title,
  image,
  href,
  affiliation,
}: ShowcaseCardProps) {
  return (
    <Link
      className="group relative flex cursor-pointer flex-col gap-2 overflow-hidden"
      href={href}
      rel="noopener noreferrer"
      target="_blank"
    >
      <div className="relative h-[300px] w-full min-w-[500px]">
        <Image
          alt={title}
          className="rounded-sm object-cover"
          fill
          sizes="(max-width: 768px) 100vw, 500px"
          src={image}
        />
      </div>

      <div className="flex flex-col">
        <div className="group inline-flex cursor-pointer items-center justify-start gap-1 font-semibold text-foreground/70 text-xl duration-200 hover:text-foreground ">
          {title}
          <ChevronRightIcon className="size-4 translate-x-0 opacity-0 transition-all duration-300 ease-out group-hover:translate-x-1 group-hover:opacity-100" />
        </div>
        <p className="text-muted-foreground text-sm">{affiliation}</p>
      </div>
    </Link>
  );
}
