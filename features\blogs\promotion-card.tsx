import { ChevronRightIcon } from 'lucide-react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';

export default function PromtionCard() {
  return (
    <div className="relative flex h-full w-full cursor-pointer flex-col items-center justify-center gap-y-3.5 overflow-hidden rounded-lg border bg-indigo-600 p-3 text-white">
      <h2 className="text-balance text-center font-semibold text-lg tracking-tighter">
        Need a website that makes a strong first impression?
      </h2>
      <p className="text-center leading-tight tracking-tight">
        We design fast, professional, and mobile-friendly websites that help
        your business stand out and grow online.
      </p>

      <Button asChild className="w-full" variant="default">
        <Link href="/contact">
          Get in Touch
          <ChevronRightIcon className="ml-2 size-4" />
        </Link>
      </Button>
    </div>
  );
}
