import {
  PageHeader,
  <PERSON>HeaderDescription,
  PageHeaderHeading,
} from './page-header';
import { ServicesCards } from './services-cards';

const longText =
  'We offer end-to-end services to help you build, launch, maintain, and scale your platform with confidence.';
const shortText =
  'Our services help you to build, launch, maintain, and scale your platform.';

export function ServicesSection() {
  return (
    <section className="border-grid" id="services">
      <PageHeader>
        <PageHeaderHeading className="font-mono text-3xl lg:text-4xl">
          Our Services
        </PageHeaderHeading>
        <PageHeaderDescription className="text-foreground/80 tracking-tight md:hidden">
          {shortText}
        </PageHeaderDescription>
        <PageHeaderDescription className="hidden text-foreground/80 tracking-tight md:block">
          {longText}
        </PageHeaderDescription>

        <ServicesCards />
      </PageHeader>
    </section>
  );
}
