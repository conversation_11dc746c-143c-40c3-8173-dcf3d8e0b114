import { MDXContent } from '@content-collections/mdx/react';
import type { Blog } from 'content-collections';
import { components } from '@/features/blogs/components';

export function BlogBody({ post }: { post: Blog }) {
  return (
    <div className="article-content col-span-5 md:border-border md:p-5 lg:border-r ">
      <MDXContent code={post?.body.code} components={components} />
    </div>
  );
}
