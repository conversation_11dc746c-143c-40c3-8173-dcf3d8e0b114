'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { navItems } from '@/lib/docs';
import { cn } from '@/lib/utils';
import { Button } from './ui/button';

export function MainNav({ className, ...props }: React.ComponentProps<'nav'>) {
  const pathname = usePathname();

  return (
    <nav className={cn('items-center gap-0.5', className)} {...props}>
      {navItems.map((item) => (
        <Button asChild key={item.href} size="sm" variant="ghost">
          <Link
            className={cn(pathname === item.href && 'text-primary')}
            href={item.href}
          >
            {item.label}
          </Link>
        </Button>
      ))}
    </nav>
  );
}
