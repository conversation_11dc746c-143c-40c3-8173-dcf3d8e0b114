import type { Blog } from 'content-collections';
import Image from 'next/image';
import Link from 'next/link';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn, formatDate } from '@/lib/utils';
import { getReadingTime } from './more-blogs-section';

type TBlogCard = {
  blog: Blog;
  isMore?: boolean;
};
export function BlogCard({ blog, isMore }: TBlogCard) {
  return (
    <Link href={`${blog.slug}`}>
      <Card className="divide relative flex h-full flex-col gap-4 rounded-sm pt-0 shadow-xs">
        <CardHeader className={cn('p-3', isMore && 'p-4')}>
          <Image
            alt="image placeholder"
            className="aspect-video w-full rounded-sm object-cover"
            height={500}
            priority
            src={blog.image || '/computer-hand.jpg'}
            width={500}
          />
        </CardHeader>
        <CardContent className="flex flex-col">
          <CardTitle className="text-base leading-5 tracking-tight">
            <p>{blog.title}</p>
          </CardTitle>
        </CardContent>
        {!isMore && (
          <CardFooter className="mt-auto flex flex-col items-start gap-2">
            <div className="flex items-center gap-x-2 text-muted-foreground text-sm">
              <span>{getReadingTime(blog.body.raw)} min read</span>
              {blog.tag && (
                <>
                  <span>·</span>
                  <span className="">{blog.tag}</span>
                </>
              )}
            </div>
            <p className="text-muted-foreground text-sm">
              {formatDate(blog.publishedOn)}
            </p>
          </CardFooter>
        )}
      </Card>
    </Link>
  );
}
