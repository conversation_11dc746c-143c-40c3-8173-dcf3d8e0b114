'use client';

import Image from 'next/image';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { GlowEffect } from '@/components/ui/glow-effect';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/features/home/<USER>';
import aboutCover from '@/public/cover.png';
import aboutCoverDark from '@/public/cover_dark.png';

export function AboutHeroSection() {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Avoid hydration mismatch
  useEffect(() => setMounted(true), []);

  const imageSrc = resolvedTheme === 'dark' ? aboutCoverDark : aboutCover;

  return (
    <section className="border-grid">
      <PageHeader className="lg:pb-8">
        <PageHeaderHeading className="font-mono text-3xl lg:text-4xl">
          About Us
        </PageHeaderHeading>
        <PageHeaderDescription className=" text-foreground/80 tracking-tight">
          We are a team of experienced web developers and designers who are
          passionate about building beautiful, functional, and accessible
          websites.
        </PageHeaderDescription>
        <PageHeaderDescription className="hidden text-foreground/80 tracking-tight md:block">
          We believe that the web should be beautiful, accessible, and
          functional for everyone. We strive to create websites that are not
          only aesthetically pleasing but also easy to use and navigate.
        </PageHeaderDescription>

        {mounted && (
          <AspectRatio
            className="relative mx-auto mt-8 max-w-4xl"
            ratio={1200 / 630}
          >
            <GlowEffect
              blur="medium"
              className="z-0 aspect-[1200/630] h-fit"
              colors={['#0894FF', '#C959DD', '#FF2E54', '#FF9004']}
              mode="static"
            />
            <Image
              alt="About cover"
              className="relative z-20 rounded-md object-cover"
              placeholder="blur"
              src={imageSrc}
            />
          </AspectRatio>
        )}
      </PageHeader>
    </section>
  );
}
