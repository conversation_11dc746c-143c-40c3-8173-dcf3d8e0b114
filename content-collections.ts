import { defineCollection, defineConfig } from '@content-collections/core';
import { compileMDX } from '@content-collections/mdx';
import rehypeAutolinkHeadings from 'rehype-autolink-headings';
import { z } from 'zod';

const showcase = defineCollection({
  name: 'Showcase',
  directory: 'content/showcase',
  include: '**/*.mdx',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    image: z.string(),
    href: z.string(),
    affiliation: z.string(),
    featured: z.boolean().optional().default(false),
  }),
  transform: async (document, context) => {
    const body = await compileMDX(context, document);
    return {
      ...document,
      slug: `/showcase/${document._meta.path}`,
      slugAsParams: document._meta.path,
      body: {
        raw: document.content,
        code: body,
      },
    };
  },
  onSuccess: (docs) => {
    // biome-ignore lint/suspicious/noConsole: <explanation>
    console.log(`generated content with ${docs.length} showcase items`);
  },
});

const blogs = defineCollection({
  name: 'Blogs',
  directory: 'content/blogs',
  include: '**/*.mdx',
  schema: z.object({
    title: z.string(),
    description: z.string().optional(),
    image: z.string().optional(),
    tag: z.string().optional(),
    author: z.string().optional(),
    publishedOn: z.string(),
    featured: z.boolean().optional().default(false),
  }),
  transform: async (document, context) => {
    const body = await compileMDX(context, document, {
      rehypePlugins: [
        [
          rehypeAutolinkHeadings,
          {
            properties: {
              className: ['subheading-anchor'],
              ariaLabel: 'Link to section',
            },
          },
        ],
      ],
    });
    return {
      ...document,
      slug: `/blogs/${document._meta.path}`,
      slugAsParams: document._meta.path,
      body: {
        raw: document.content,
        code: body,
      },
    };
  },
  onSuccess: (docs) => {
    // biome-ignore lint/suspicious/noConsole: <explanation>
    console.log(`generated content with ${docs.length} blogs`);
  },
});

export default defineConfig({
  collections: [showcase, blogs],
});
