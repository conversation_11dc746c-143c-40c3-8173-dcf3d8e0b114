{"name": "rathon", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@calcom/embed-react": "^1.5.3", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@react-email/components": "^0.1.1", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "geist": "^1.4.2", "lucide-react": "^0.525.0", "motion": "^12.23.3", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "resend": "^4.6.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@content-collections/core": "^0.9.1", "@content-collections/mdx": "^0.2.2", "@content-collections/next": "^0.2.6", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "rehype-autolink-headings": "^7.1.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "ultracite": "5.0.32", "zod": "^3.25.74"}}