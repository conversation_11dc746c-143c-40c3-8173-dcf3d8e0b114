import { QuoteIcon } from 'lucide-react';
import { testimonials } from '@/lib/docs';
import { cn } from '@/lib/utils';
import { PageHeader, PageHeaderDescription } from './page-header';
import { TestimonialStars } from './testimonial-stars';

export function TestimonialsSection() {
  return (
    <section className="border-grid bg-muted ">
      <PageHeader className="py-16 text-left">
        <div className="mx-auto max-w-6xl">
          <div className="grid grid-cols-1 rounded-2xl md:grid-cols-3">
            {testimonials.map((t, i) => (
              <div
                className={cn(
                  'flex flex-col justify-between gap-10 bg-background p-6 shadow-sm ring-1 ring-muted',
                  i === 0 && 'rounded-t-lg md:rounded-t-none md:rounded-l-lg',
                  i === 2 && 'rounded-b-lg md:rounded-r-lg md:rounded-b-none'
                )}
                key={t.name}
              >
                <div className="relative flex items-start gap-3">
                  <QuoteIcon className="absolute hidden size-10 text-muted-foreground opacity-10 lg:block" />
                  <PageHeaderDescription className="flex-1/4 tracking-tight sm:text-base">
                    {t.content}
                  </PageHeaderDescription>
                </div>

                <div className="flex flex-col gap-2">
                  <h3 className="font-bold text-foreground text-lg">
                    {t.organization}
                  </h3>
                  <p className="font-mono text-foreground/80 text-sm">
                    {t.role}, {t.name}
                  </p>
                  <TestimonialStars stars={t.stars} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </PageHeader>
    </section>
  );
}
