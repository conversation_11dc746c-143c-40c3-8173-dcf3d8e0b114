import { TextRoll } from '@/components/custom/text-roll';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/features/home/<USER>';

export function ServicesHeroSection() {
  return (
    <section className="border-grid">
      <PageHeader className="lg:pb-8">
        <PageHeaderHeading>
          <TextRoll
            className="font-mono text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl"
            variants={{
              enter: {
                initial: { rotateX: 0, filter: 'blur(0px)' },
                animate: { rotateX: 90, filter: 'blur(2px)' },
              },
              exit: {
                initial: { rotateX: 90, filter: 'blur(2px)' },
                animate: { rotateX: 0, filter: 'blur(0px)' },
              },
            }}
          >
            Services
          </TextRoll>
        </PageHeaderHeading>
        <PageHeaderDescription className="text-foreground/80 tracking-tight">
          We design and build websites that combine beauty, functionality, and
          accessibility. Every project is tailored to your brand and goals.
        </PageHeaderDescription>
      </PageHeader>
    </section>
  );
}
