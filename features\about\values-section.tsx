'use client';
import { LayoutTemplateIcon, LightbulbIcon, ZapIcon } from 'lucide-react';
import { motion } from 'motion/react';
import { InView } from '@/components/ui/in-view';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/features/home/<USER>';

const values = [
  {
    name: 'Convert',
    content:
      'Turn visitors into customers with smart, goal-driven design and strategy.',
    icon: LightbulbIcon,
  },
  {
    name: 'Clean',
    content:
      'Minimal, modern layouts that feel intentional, elegant, and easy.',
    icon: LayoutTemplateIcon,
  },
  {
    name: 'Fast',
    content:
      'Lightning-speed performance for better UX, SEO, and user retention.',
    icon: ZapIcon,
  },
];

export function ValuesSection() {
  return (
    <section className="border-grid">
      <PageHeader className="">
        <PageHeaderHeading className="font-mono text-3xl lg:text-4xl">
          Our Values
        </PageHeaderHeading>
        <PageHeaderDescription className=" text-foreground/80 tracking-tight">
          We believe that the web should be beautiful, accessible, and
          functional for everyone.
        </PageHeaderDescription>
        <InView
          variants={{
            hidden: {
              opacity: 0,
            },
            visible: {
              opacity: 1,
              transition: {
                staggerChildren: 0.09,
              },
            },
          }}
          viewOptions={{ once: false, margin: '0px 0px -250px 0px' }}
        >
          <div className="relative mt-8 grid divide-x divide-y divide-dashed border border-grid *:p-12 sm:grid-cols-2 md:mt-14 lg:grid-cols-3">
            {values.map((value) => (
              <motion.div
                className="flex flex-col items-center justify-center gap-6 bg-background"
                key={value.name}
                variants={{
                  hidden: { opacity: 0, scale: 0.8, filter: 'blur(10px)' },
                  visible: {
                    opacity: 1,
                    scale: 1,
                    filter: 'blur(0px)',
                  },
                }}
              >
                <value.icon className="size-10 " />
                <p className="font-semibold text-2xl text-muted-foreground leading-8 tracking-tight">
                  <span className="text-primary">{value.name}.</span>{' '}
                  {value.content}
                </p>
              </motion.div>
            ))}
          </div>
        </InView>
      </PageHeader>
    </section>
  );
}
