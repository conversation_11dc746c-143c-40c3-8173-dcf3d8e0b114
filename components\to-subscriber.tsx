import {
  <PERSON>,
  <PERSON><PERSON>,
  Column,
  Con<PERSON>er,
  <PERSON><PERSON>,
  <PERSON>,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

interface EmailTemplateProps {
  email: string;
}

export const ThankYouForSubscribingTemplate = ({
  email,
}: EmailTemplateProps) => {
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });

  const safeEmail = email || '[No email provided]';

  return (
    <Html>
      <Head>
        <Font
          fallbackFontFamily="Arial"
          fontFamily="Roboto"
          fontStyle="normal"
          fontWeight={400}
          webFont={{
            url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
            format: 'woff2',
          }}
        />
      </Head>
      <Preview>Thank you for subscribing to <PERSON><PERSON>!</Preview>
      <Tailwind>
        <Body className="mx-auto my-auto bg-gray-50 font-sans">
          <Container className="mx-auto my-8 max-w-[600px]">
            {/* Header */}
            <Section className="rounded-t-lg bg-blue-600 px-8 py-6">
              <Row>
                <Column>
                  <Text className="text-center font-bold text-3xl text-white">
                    Rathon
                  </Text>
                </Column>
              </Row>
            </Section>

            {/* Main Content */}
            <Section className="rounded-b-lg bg-white px-8 py-10 shadow-sm">
              <Row>
                <Column>
                  <Text className="mb-5 font-bold text-2xl text-gray-800">
                    You're subscribed!
                  </Text>

                  <Text className="mb-5 text-gray-700">
                    Thank you for subscribing to Rathon! We're excited to have
                    you on board and look forward to sharing updates, insights,
                    and exclusive content with you.
                  </Text>

                  <Container className="mb-6 rounded-md border-green-500 border-l-4 bg-green-50 p-5">
                    <Text className="mb-1 font-medium text-base text-green-800">
                      Your Subscription Info:
                    </Text>
                    <Text className="mb-0 text-green-800">
                      <strong>Email:</strong> {safeEmail}
                    </Text>
                    <Text className="mb-0 text-green-800">
                      <strong>Subscribed on:</strong> {currentDate}
                    </Text>
                  </Container>

                  <Section className="mb-8 text-center">
                    <Button
                      className="rounded-md bg-blue-600 px-6 py-3 font-medium text-white hover:bg-blue-700"
                      href="https://rathon-rw.vercel.app/blogs"
                    >
                      Visit Our Blog
                    </Button>
                  </Section>

                  <Hr className="my-6 border-gray-200" />

                  <Text className="mb-4 text-gray-700">
                    We’ll only send you meaningful updates. You can unsubscribe
                    at any time using the link below.
                  </Text>
                </Column>
              </Row>
            </Section>

            {/* Footer */}
            <Section className="px-8 py-6">
              <Text className="text-center text-gray-500 text-xs">
                © {new Date().getFullYear()} Rathon. All rights reserved.
              </Text>
              <Text className="text-center text-gray-500 text-xs">
                You’re receiving this because you subscribed on our website.
              </Text>
              <Text className="text-center text-gray-500 text-xs">
                <Link className="text-blue-500 underline" href="#">
                  Unsubscribe
                </Link>{' '}
                from these emails
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};
