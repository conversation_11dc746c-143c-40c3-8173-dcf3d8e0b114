'use client';
import { motion } from 'motion/react';
import { InView } from '@/components/ui/in-view';
import { services } from '@/lib/docs';

export function ServicesCards() {
  return (
    <div>
      <InView
        variants={{
          hidden: {
            opacity: 0,
          },
          visible: {
            opacity: 1,
            transition: {
              staggerChildren: 0.09,
            },
          },
        }}
        viewOptions={{ once: false, margin: '0px 0px -250px 0px' }}
      >
        <div className="relative mt-10 grid divide-x divide-y divide-dashed rounded-sm border border-grid text-left *:p-8 sm:grid-cols-2 md:*:p-12 lg:grid-cols-3">
          {services.map((service) => (
            <motion.div
              className="flex flex-col gap-4"
              key={service.name}
              variants={{
                hidden: { opacity: 0, scale: 0.8, filter: 'blur(10px)' },
                visible: {
                  opacity: 1,
                  scale: 1,
                  filter: 'blur(0px)',
                },
              }}
            >
              <div className="flex items-center gap-2">
                <service.icon className="size-5" />
                <h5 className="text-balance font-medium text-lg tracking-tighter">
                  {service.name}
                </h5>
              </div>
              <p className="text-muted-foreground leading-tight">
                {service.content}
              </p>
            </motion.div>
          ))}
        </div>
      </InView>
    </div>
  );
}
