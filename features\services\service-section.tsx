import { ImageIcon, YoutubeIcon } from 'lucide-react';
import { HeroVideoDialog } from '@/components/custom/hero-video-dialog';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/features/home/<USER>';
import design from '@/public/service/design.jpg';
export default function ServiceSection() {
  return (
    <PageHeader className="items-start gap-10 text-left">
      <PageHeaderHeading className="font-mono text-2xl md:px-6 lg:text-3xl xl:text-4xl">
        Web Design
      </PageHeaderHeading>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 md:px-6">
        <div className=" flex flex-col gap-5 lg:gap-7">
          <PageHeaderDescription className=" text-foreground/80 tracking-tight">
            Our web design service transforms your ideas into visually stunning,
            user-focused websites that work flawlessly on any device. We blend
            creativity with strategy to ensure every page reflects your
            brand&apos;s identity while guiding visitors toward action.
          </PageHeaderDescription>
          <PageHeaderDescription className=" text-foreground/80 tracking-tight">
            With clean layouts, intuitive navigation, and fast-loading pages, we
            design for both humans and search engines helping you attract more
            visitors, keep them engaged, and turn clicks into customers.
          </PageHeaderDescription>
          <div className="flex w-fit flex-col gap-2">
            <div className="flex items-center gap-2 text-muted-foreground text-sm hover:text-primary">
              <ImageIcon className="size-4" />
              <a
                className=""
                href="https://www.pexels.com/photo/notebook-beside-the-iphone-on-table-196644/"
                rel="noopener noreferrer"
                target="_blank"
              >
                Photo by picjumbo.com
              </a>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground text-sm hover:text-primary">
              <YoutubeIcon className="size-4" />
              <a
                href="https://www.youtube.com/watch?v=JoSvVftJ7B0"
                rel="noopener noreferrer"
                target="_blank"
              >
                Video by Hostinger Academy
              </a>
            </div>
          </div>
        </div>
        <div className="-order-1 relative">
          <HeroVideoDialog
            animationStyle="top-in-bottom-out"
            className="block"
            thumbnailAlt="Hero Video"
            thumbnailSrc={design.src}
            videoSrc="https://www.youtube.com/embed/JoSvVftJ7B0?si=Yits5YdM64pBK7F2"
          />
        </div>
      </div>
    </PageHeader>
  );
}
