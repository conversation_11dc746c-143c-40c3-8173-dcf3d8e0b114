'use client';
import Cal, { getCal<PERSON><PERSON> } from '@calcom/embed-react';
import { useEffect } from 'react';
import { PageHeader } from '../home/<USER>';
export default function CalEmbededSection() {
  useEffect(() => {
    (async () => {
      const cal = await getCalApi({ namespace: '30min' });
      cal('ui', { hideEventTypeDetails: false, layout: 'month_view' });
    })();
  }, []);
  return (
    <section className="border-grid">
      <PageHeader className="py-16 text-left">
        <Cal
          calLink="rathon-webdev/30min"
          config={{ layout: 'month_view' }}
          namespace="30min"
          style={{ width: '100%', height: '100%', overflow: 'scroll' }}
        />
      </PageHeader>
    </section>
  );
}
