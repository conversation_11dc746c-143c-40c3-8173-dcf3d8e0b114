import { TrendingDownIcon } from "lucide-react";
import Link from "next/link";
import { Announcement } from "@/components/announcement";
import { Button } from "@/components/ui/button";
import { GlowButton } from "@/components/ui/glow-button";
import { PointerHighlight } from "@/components/ui/pointer-highlight";
import { Spotlight } from "@/components/ui/spotlight";
import {
  PageActions,
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from "./page-header";

const phoneDescription =
  "We build bold brands and powerful platforms to help your business stand out and scale fast.";
const computerDescription =
  "We don’t just build websites. We craft bold brands and powerful platforms for businesses ready to stand out, scale up, and dominate the digital space.";

export function HeroSection() {
  return (
    <section className="relative border-grid">
      <Spotlight
        className="-top-40 md:-top-20 left-0 hidden sm:block md:left-60"
        fill="white"
      />
      <PageHeader className="flex min-h-[calc(100vh-150px)] flex-col items-center justify-center gap-11 md:min-h-fit md:gap-6">
        <Announcement />
        <PageHeaderHeading className="leading-12 md:leading-tight">
          Web Solutions for{" "}
          <PointerHighlight
            containerClassName="inline-flex"
            rectangleClassName="border-dashed rounded-sm"
          >
            <span className="px-2">Visionary Brands</span>
          </PointerHighlight>
        </PageHeaderHeading>
        <PageHeaderDescription className="max-w-4xl md:hidden">
          {phoneDescription}
        </PageHeaderDescription>
        <PageHeaderDescription className="hidden max-w-4xl md:block">
          {computerDescription}
        </PageHeaderDescription>
        <div className="flex flex-col gap-4">
          <PageActions className="flex flex-col gap-5 sm:flex-row md:gap-6">
            <GlowButton asChild className="w-full rounded-sm" size={"lg"}>
              <Link href="/contact">Get Started</Link>
            </GlowButton>

            <Button
              asChild
              className="w-full rounded-sm sm:w-fit"
              size="lg"
              variant="secondary"
            >
              <Link href="/book">Book a Consultation</Link>
            </Button>
          </PageActions>
          <p className="text-muted-foreground text-sm">
            No more{" "}
            <TrendingDownIcon
              aria-hidden="true"
              className="inline-block size-4"
            />{" "}
            <span className="font-bold">Business downfalls</span>
          </p>
        </div>
      </PageHeader>
    </section>
  );
}
