import { unstable_ViewTransition as ViewTransition } from 'react';
import { FooterCta } from '@/components/footer-cta';
import { SiteFooter } from '@/components/site-footer';
import { SiteHeader } from '@/components/site-header';

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative z-10 flex min-h-svh flex-col bg-background">
      <SiteHeader />
      <main className="relative flex flex-1 flex-col">
        <ViewTransition name="page">{children}</ViewTransition>
      </main>
      <FooterCta />
      <SiteFooter />
    </div>
  );
}
