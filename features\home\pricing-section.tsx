'use client';
import { CheckIcon } from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { InView } from '@/components/ui/in-view';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { pricingPlans } from '@/lib/docs';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from './page-header';

const longText =
  'Flexible pricing that fits your business needs from design to development. Pay for exactly what you need and when you need it.';
const shortText =
  'Flexible pricing that fits your business needs. Pay for exactly what you need.';

export function PricingSection() {
  const [isRwf, setIsRwf] = useState(false);
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    const stored = localStorage.getItem('isRwf');
    setIsRwf(stored === 'true');
    setHasMounted(true); // wait until mounted to render component
  }, []);

  const handleSwitchChange = (checked: boolean) => {
    setIsRwf(checked);
    localStorage.setItem('isRwf', checked.toString());
  };

  if (!hasMounted) {
    return null;
  } // avoid mismatched hydration

  return (
    <section className="border-grid" id="pricing">
      <PageHeader className="py-16">
        <PageHeaderHeading className="font-mono text-3xl lg:text-4xl">
          Pricing
        </PageHeaderHeading>
        <PageHeaderDescription className=" text-foreground/80 tracking-tight md:hidden">
          {shortText}
        </PageHeaderDescription>
        <PageHeaderDescription className="hidden text-foreground/80 tracking-tight md:block">
          {longText}
        </PageHeaderDescription>
        <div className="flex items-center gap-2">
          <Switch
            aria-checked={isRwf}
            checked={isRwf}
            className="cursor-pointer"
            id="switch"
            onCheckedChange={handleSwitchChange}
          />
          <Label htmlFor="switch">In Rwandan Francs</Label>
        </div>
        <InView
          variants={{
            hidden: {
              opacity: 0,
            },
            visible: {
              opacity: 1,
              transition: {
                staggerChildren: 0.09,
              },
            },
          }}
          viewOptions={{ once: false, margin: '0px 0px -250px 0px' }}
        >
          <div className="mt-8 grid gap-10 text-left md:mt-20 md:grid-cols-3 md:gap-6">
            {pricingPlans.map((plan) => (
              <motion.div
                className="flex flex-col"
                key={plan.title}
                variants={{
                  hidden: { opacity: 0, scale: 0.8, filter: 'blur(10px)' },
                  visible: {
                    opacity: 1,
                    scale: 1,
                    filter: 'blur(0px)',
                  },
                }}
              >
                <Card
                  className={`flex flex-1 flex-col rounded-sm ${
                    plan.tag === 'Popular' ? 'relative' : ''
                  }`}
                >
                  {plan.tag && (
                    <span className="-top-3 absolute inset-x-0 mx-auto flex h-6 w-fit items-center rounded-full bg-primary px-3 py-1 font-medium text-primary-foreground text-xs ring-2 ring-secondary">
                      {plan.tag}
                    </span>
                  )}

                  <CardHeader>
                    <CardTitle className="font-medium">{plan.title}</CardTitle>
                    {plan.price && (
                      <span className="my-3 block font-semibold text-2xl">
                        {isRwf ? plan.price.rfw : plan.price.usd}
                      </span>
                    )}
                    <CardDescription className="text-sm">
                      {plan.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <hr className="border-dashed" />
                    <ul className="list-outside space-y-3 text-sm">
                      {plan.features.map((feature) => (
                        <li className="flex items-center gap-2" key={feature}>
                          <CheckIcon className="h-4 w-4" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>

                  <CardFooter className="mt-auto flex items-center justify-center">
                    <Button
                      asChild
                      className="w-full"
                      variant={
                        plan.buttonVariant as
                          | 'outline'
                          | 'default'
                          | 'link'
                          | 'destructive'
                          | 'secondary'
                          | 'ghost'
                      }
                    >
                      <Link href="/contact">Get Started</Link>
                    </Button>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </div>
        </InView>
      </PageHeader>
    </section>
  );
}
