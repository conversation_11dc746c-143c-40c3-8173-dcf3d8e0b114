'use client';
import { motion } from 'motion/react';
import { AnimatedCircularProgressBar } from '@/components/ui/animated-circular-progress-bar';
import { InView } from '@/components/ui/in-view';

import { performanceData } from '@/lib/docs';

export function AnimatedProgress() {
  return (
    <div className="mx-auto mt-8 w-full max-w-3xl gap-4 px-6 md:mt-10 lg:mt-16 lg:px-0">
      <InView
        variants={{
          hidden: {
            opacity: 0,
          },
          visible: {
            opacity: 1,
            transition: {
              staggerChildren: 0.09,
            },
          },
        }}
        viewOptions={{ once: false, margin: '0px 0px -250px 0px' }}
      >
        <div className="flex flex-wrap items-center justify-center gap-10 md:justify-between">
          {performanceData.map((p) => (
            <motion.div
              className="flex flex-col items-center justify-center gap-10"
              key={p.title}
              variants={{
                hidden: { opacity: 0, scale: 0.8, filter: 'blur(10px)' },
                visible: {
                  opacity: 1,
                  scale: 1,
                  filter: 'blur(0px)',
                },
              }}
            >
              <AnimatedCircularProgressBar
                className="size-28 md:size-32"
                gaugePrimaryColor="rgb(79 70 229)"
                gaugeSecondaryColor="var(--color-muted)"
                max={100}
                min={0}
                value={p.value}
              />
              <p className="text-center font-medium font-mono text-lg text-muted-foreground leading-tight tracking-tighter">
                {p.title}
              </p>
            </motion.div>
          ))}
        </div>
      </InView>
    </div>
  );
}
