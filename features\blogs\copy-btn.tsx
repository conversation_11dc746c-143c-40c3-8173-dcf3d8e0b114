// components/CopyButton.tsx
'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

interface CopyButtonProps {
  text: string;
}

export default function CopyButton({ text }: CopyButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      toast('You copied the following link:', {
        description: text,
      });
      setCopied(true);
      setTimeout(() => setCopied(false), 2000); // Reset after 2s
    } catch {
      toast.error('Failed to copy the link. Please try again.', {
        description: 'There was an error copying the link.',
      });
    }
  };

  return (
    <Button
      className="rounded-sm"
      onClick={handleCopy}
      size="sm"
      variant="secondary"
    >
      {copied ? 'Copied!' : 'Copy'}
    </Button>
  );
}
