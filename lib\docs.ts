import {
  CoinsIcon,
  Cpu,
  FeatherIcon,
  Fingerprint,
  HouseIcon,
  MessageCircleIcon,
  Pencil,
  Settings2,
  ShoppingCartIcon,
  Sparkles,
  User2Icon,
  WorkflowIcon,
  Zap,
} from "lucide-react";
import abTemplate from "@/public/template/ab-template2.png";
export const navItems = [
  {
    href: "/",
    label: "Home",
    icon: HouseIcon,
  },
  {
    href: "/about",
    label: "About",
    icon: User2Icon,
  },
  {
    href: "/services",
    label: "Services",
    icon: WorkflowIcon,
  },
  {
    href: "/marketplace",
    label: "Marketplace",
    icon: ShoppingCartIcon,
  },
  {
    href: "/blogs",
    label: "Blogs",
    icon: FeatherIcon,
  },
  {
    href: "/#pricing",
    label: "Pricing",
    icon: CoinsIcon,
  },
  {
    href: "/contact",
    label: "Contact",
    icon: MessageCircleIcon,
  },
];
export const links = [
  {
    group: "Services",
    items: [
      {
        title: "Web Design",
        href: "/#services",
        external: false,
      },
      {
        title: "Web development",
        href: "/#services",
        external: false,
      },
      {
        title: "UI/UX Design",
        href: "/#services",
        external: false,
      },
      {
        title: "Seo Optimization",
        href: "/#services",
        external: false,
      },
      {
        title: "Hosting Website",
        href: "/#services",
        external: false,
      },
      {
        title: "Maintaing website",
        href: "/#services",
        external: false,
      },
    ],
  },
  {
    group: "Company",
    items: [
      {
        title: "About",
        href: "/about",
        external: false,
      },
      {
        title: "Blog",
        href: "/blogs",
        external: false,
      },
      {
        title: "Pricing",
        href: "/#pricing",
        external: false,
      },
      {
        title: "Customers",
        href: "/#testimonials",
        external: false,
      },
      {
        title: "Marketplace",
        href: "/marketplace",
        external: false,
      },
      {
        title: "Book a Call",
        href: "/book",
        external: false,
      },
    ],
  },
  {
    group: "Resources",
    items: [
      {
        title: "Careers",
        href: "/contact",
        external: false,
      },

      {
        title: "Support",
        href: "/book",
        external: false,
      },
      {
        title: "Videos",
        href: "https://www.youtube.com/@RathonRw",
        external: true,
      },
      {
        title: "Privacy",
        href: "/privacy",
        external: false,
      },
      {
        title: "Terms",
        href: "/terms",
        external: false,
      },
      // sitemap
      {
        title: "Sitemap",
        href: "/sitemap.xml",
        external: false,
      },
    ],
  },

  // working hours
  {
    group: "Working Hours",
    items: [
      {
        title: "Monday - Friday",
        href: "/about",
        external: false,
      },
      {
        title: "8:00 AM - 5:00 PM",
        href: "/about",
        external: false,
      },
      {
        title: "Saturday - Sunday",
        href: "/about",
        external: false,
      },
      {
        title: "Closed",
        href: "/about",
        external: false,
      },
    ],
  },
];

export const services = [
  {
    name: "Web Design",
    content:
      "Modern, responsive designs that reflect your brand and engage your audience.",
    icon: Zap,
  },
  {
    name: "Web Development",
    content:
      "Custom websites and apps built for speed, scalability, and reliability.",
    icon: Cpu,
  },
  {
    name: "UI/UX Design",
    content: "User-friendly interfaces that improve engagement and conversion.",
    icon: Fingerprint,
  },
  {
    name: "SEO Optimization",
    content:
      "Boost your search ranking and drive more organic traffic to your site.",
    icon: Pencil,
  },
  {
    name: "Hosting Websites",
    content: "Fast, secure, and reliable hosting with minimal downtime.",
    icon: Settings2,
  },
  {
    name: "Maintaining Websites",
    content:
      "Ongoing updates, backups, and monitoring to keep your site running smoothly.",
    icon: Sparkles,
  },
];

export const pricingPlans = [
  {
    title: "Web Design",
    price: {
      usd: "$150",
      rfw: "200,000 RWF",
    },
    description: "Per website",
    features: [
      "Custom Website Layout",
      "Mobile-Responsive Layout",
      "Fast & Lightweight Pages",
      "Not more than 4 pages",
      "24/7 Support",
    ],
    buttonVariant: "outline",
    tag: null,
  },
  {
    title: "Web Development",
    price: {
      usd: "$250",
      rfw: "350,000 RWF",
    },
    description: "Per website",
    features: [
      "Everything in Web Design Plan",
      "Not more than 4 pages",
      "Custom Functionality & Features",
      "Monthly Updates & Maintenance",
      "Advanced Security Features",
      "24/7 Priority Support",
    ],
    buttonVariant: "default",
    tag: "Popular",
  },
  {
    title: "Custom Website / Services",
    price: null,
    description: "Web design and development",
    features: [
      "Everything in Web Development Plan",
      "Multi-Page Website (More than 4 Pages)",
      "Dark Mode Support",
      "Advanced SEO Optimization",
      "Premium Speed",
      "Performance Optimization",
      "24/7 Priority Support",
    ],
    buttonVariant: "outline",
    tag: null,
  },
];

export const performanceData = [
  {
    title: "Performance",
    value: 100,
  },
  {
    title: "Accessibility",
    value: 98,
  },
  {
    title: "Best Practices",
    value: 100,
  },
  {
    title: "SEO",
    value: 100,
  },
];

export const testimonials = [
  {
    content: `I give Rethon 5 stars and. recommend it to everyone looking for the websites of his/her dreams. For reliability and efficiency trust Rathon and you will not regret. Rathon is a solution to this technological era where businesses and services are explored as they are on everyone's screen.`,
    organization: "Academie Dela Salle",
    role: "DOS",
    name: "Brother Theoneste",
    stars: 5,
  },
  {
    content:
      "As a freelance developer, I’ve worked with many agencies, but Rathon stands out. Their attention to detail in design, their deep understanding of SEO, and the way they bring ideas to life through development is unmatched. They don't just build websites they build growth engines. I always recommend Rathon to clients who want results.",
    organization: "Independent Freelancer",
    role: "Full-Stack Developer",
    name: "Alex Niyonsaba",
    stars: 5,
  },
  {
    content:
      "Collaborating with Rathon has been one of the most seamless experiences of my career. Not only are they great at what they do, but they genuinely care about the businesses they work with. Their team is sharp, humble, and incredibly professional. If you're serious about growing your brand online, you won’t find a better partner.",
    organization: "Freelance Tech Consultant",
    role: "Frontend Developer",
    name: "Elodie Mukamana",
    stars: 5,
  },
];

export const faqs = [
  {
    value: "faq-1",
    question: "How long does it take to build a website?",
    answer:
      "It really depends on what you need. Most websites take around 3 to 6 weeks. If it’s something simple, it can be faster. Bigger or more custom sites take a bit more time.",
  },
  {
    value: "faq-2",
    question: "Will my website work on phones and tablets?",
    answer:
      "Yes, for sure. Every site we build is fully responsive, so it’ll look and work great on phones, tablets, laptops and any other devices.",
  },
  {
    value: "faq-3",
    question: "Can I make changes to the site after it's done?",
    answer:
      "Definitely. We make sure you have access to update things like text and images yourself. And if you ever need help, we’re here for that too.",
  },
  {
    value: "faq-4",
    question: "Where can I find your pricing?",
    answer:
      "We’ve got a full pricing page with everything you need to know. Feel free to check it out or reach out if you’re unsure what fits best.",
  },
  {
    value: "faq-5",
    question: "What if I don’t have a logo or branding yet?",
    answer:
      "No problem at all. We can help you create a simple brand identity like a logo, colors, and fonts to make sure your site feels polished and consistent.",
  },
  {
    value: "faq-6",
    question: "Will my site show up on Google?",
    answer:
      "Yes! We follow SEO best practices so your site is search-engine friendly from the start. If you want to go further, we can also help with advanced SEO strategies.",
  },
  {
    value: "faq-7",
    question: "Do I need to buy a domain or hosting myself?",
    answer:
      "We can guide you through it or handle it for you whatever works best. If you already have a domain or hosting, we’ll work with what you’ve got.",
  },
  {
    value: "faq-8",
    question: "How do payments work?",
    answer:
      "We usually split payments into two or three stages like a deposit to start, one during the build, and the final amount on launch. We'll always be clear and flexible with you.",
  },
  {
    value: "faq-9",
    question: "Do you offer support after the website is live?",
    answer:
      "Yes! We’re here if you need updates, fixes, or just some guidance. We also offer optional maintenance plans if you’d like us to handle everything for you.",
  },
];

export const terms = [
  {
    title: "Using Our Website",
    content:
      "You're free to browse, read, and learn from our content. But please don't copy, steal, or republish our work without permission. That includes text, visuals, and code we've written.",
  },
  {
    title: "Subscribing to Our Newsletter",
    content:
      "If you subscribe to our newsletter, we'll send you emails from time to time with web design tips, agency updates, and useful content. You can unsubscribe anytime no questions asked.",
  },
  {
    title: "Contact Form",
    content:
      "If you reach out through our contact form, please be respectful and honest. We're here to help, not to deal with spam or abuse.",
  },
  {
    title: "No Guarantees",
    content:
      "While we do our best to provide accurate, helpful information and great service, we can't make any promises about specific results. Every project and client is unique.",
  },
  {
    title: "Third-Party Links",
    content:
      "Sometimes we may link to tools or platforms we love. We're not responsible for anything that happens on those third-party sites  so use your best judgment when leaving our site.",
  },
  {
    title: "Changes to These Terms",
    content:
      "We may update these terms from time to time. If it's a big change, we'll let you know. If it's something small, we'll quietly update the page. Either way, the latest version is what applies.",
  },
  {
    title: "Contact Us",
    content:
      "Got questions about our terms? Want to partner with us or just say hi? Reach <NAME_EMAIL>.",
  },
];

export const templates = [
  {
    title: "AB Consulting",
    description: "This is a  for a prebuilt website of consulting company",
    image: abTemplate,
    href: "https://ab-consulting.vercel.app/",
  },
];

export const servicesContent= [
  {
    title: "Web Design",
    image: {
      src: "/service/design.jpg",
      href: "https://www.pexels.com/photo/notebook-beside-the-iphone-on-table-196644/",
      label: "Photo by picjumbo.com",
    },
    video:{
      src: "https://www.youtube.com/embed/JoSvVftJ7B0?si=Yits5YdM64pBK7F2",
      href: "https://www.youtube.com/watch?v=JoSvVftJ7B0",
      label: "Video by Hostinger Academy",
    },
    content: {
      "",
      
    }
   
  },
  
]
