'use client';

import { useEffect, useState } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';

// Helper function to generate consistent IDs
const generateHeadingId = (text: string) => {
  return text
    .toLowerCase()
    .replace(/\s*&\s*/g, '--')
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

export default function BlogTableOfContents({
  headings: initialHeadings,
}: {
  headings: string[];
}) {
  const [activeHeading, setActiveHeading] = useState<string>('');
  const [headings, setHeadings] = useState<string[]>(initialHeadings);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Extract headings from rendered DOM
    const extractHeadings = () => {
      const headingElements =
        document.querySelector('.article-content')?.querySelectorAll('h2') ||
        [];
      const extractedHeadings: string[] = [];
      for (const element of headingElements) {
        if (element.textContent) {
          extractedHeadings.push(element.textContent);
        }
      }
      setHeadings(extractedHeadings);
      setIsLoading(false);
    };

    // Extract headings after a short delay to ensure content is rendered
    const timeoutId = setTimeout(extractHeadings, 100);

    const observer = new IntersectionObserver(
      (entries) => {
        for (const entry of entries) {
          if (entry.isIntersecting) {
            setActiveHeading(entry.target.id);
          }
        }
      },
      {
        rootMargin: '-100px 0px -66%',
        threshold: [0, 1],
      }
    );

    const headingElements =
      document.querySelector('.article-content')?.querySelectorAll('h2') || [];
    for (const element of headingElements) {
      observer.observe(element);
    }

    // Also update active heading on scroll
    const onScroll = () => {
      const headingElementsArr = Array.from(
        document.querySelector('.article-content')?.querySelectorAll('h2') || []
      );
      const middle = window.innerHeight / 2;

      for (const heading of headingElementsArr) {
        const { top, bottom } = heading.getBoundingClientRect();
        if (top <= middle && bottom >= middle) {
          setActiveHeading(heading.id);
          break;
        }
      }
    };

    window.addEventListener('scroll', onScroll, { passive: true });
    onScroll(); // Initial check

    return () => {
      clearTimeout(timeoutId);
      for (const element of headingElements) {
        observer.unobserve(element);
      }
      window.removeEventListener('scroll', onScroll);
    };
  }, []);

  const handleClick = (headingId: string) => {
    setActiveHeading(headingId);
  };

  return (
    <div className="mb-6 w-full">
      <h3 className="mb-4 font-medium text-primary underline underline-offset-8">
        On this page
      </h3>
      <ScrollArea className="h-48 w-full">
        <div className="flex w-full flex-col gap-2.5 pr-2">
          {isLoading ? (
            // Skeleton loading state
            <div className="flex w-full flex-col gap-2.5">
              <Skeleton className="h-5 w-1/2" />
              <Skeleton className="h-5 w-2/3" />
              <Skeleton className="h-5 w-full" />
            </div>
          ) : (
            headings.map((heading, i) => {
              // Get the actual heading element to check its ID
              const headingElement = document.querySelector(
                `.article-content h2:nth-of-type(${i + 1})`
              );
              const headingId =
                headingElement?.id || generateHeadingId(heading);
              const isActive = activeHeading === headingId;

              return (
                <a
                  className={`flex items-center gap-2 font-medium text-[15px] tracking-tight transition-colors ${
                    isActive
                      ? 'text-primary'
                      : 'text-muted-foreground hover:text-primary'
                  }`}
                  href={`#${headingId}`}
                  key={headingId}
                  onClick={() => handleClick(headingId)}
                >
                  {heading}
                </a>
              );
            })
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
