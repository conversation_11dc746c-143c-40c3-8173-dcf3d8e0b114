import Link from 'next/link';
import {
  <PERSON><PERSON>eader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/features/home/<USER>';
import { cn } from '@/lib/utils';
import { Button } from './ui/button';
import { GlowButton } from './ui/glow-button';

export function FooterCta() {
  return (
    <section className="relative border-grid bg-mute">
      <div
        className={cn(
          'absolute inset-0 z-0',
          '[background-size:30px_30px]',
          '[background-image:linear-gradient(to_right,#e4e4e7_1px,transparent_1px),linear-gradient(to_bottom,#e4e4e7_1px,transparent_1px)]',
          'dark:[background-image:linear-gradient(to_right,#262626_1px,transparent_1px),linear-gradient(to_bottom,#262626_1px,transparent_1px)]'
        )}
      />
      <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-background [mask-image:radial-gradient(ellipse_at_center,transparent_5%,black)]" />
      <PageHeader className="relative z-20 bg-background/10 py-16 text-left">
        <div className="flex w-full flex-wrap items-center justify-between gap-10 md:px-6 lg:flex-nowrap">
          <div className="flex-1 space-y-8">
            <PageHeaderHeading>
              Ready to build and scale your online presence?
            </PageHeaderHeading>
            <PageHeaderDescription>
              It's time to take your business to the next level. Let's build a
              platforms that will help you achieve your goals.
            </PageHeaderDescription>
          </div>
          <div className="flex w-full max-w-96 flex-1 flex-col gap-10">
            <GlowButton asChild className="w-full" size={'lg'}>
              <Link href="/contact">Get Started</Link>
            </GlowButton>
            <Button asChild size={'lg'} variant={'secondary'}>
              <Link href="/book">Book a Consultation</Link>
            </Button>
          </div>
        </div>
      </PageHeader>
    </section>
  );
}
