"use server";

import { Resend } from "resend";
import { EmailTemplate } from "@/features/contact/email-template";
import { ContactFormSchema } from "./schema";
import type { TContactSchema } from "./types";

// Initialize Resend with API key from environment variables
const resendClient = new Resend(process.env.RESEND_API_KEY);

/**
 * Sends an email using the Resend API with the provided contact form data.
 *
 * @param data - Validated contact form data
 * @returns A result object indicating success or failure
 */
export async function sendContactEmail(data: TContactSchema) {
  try {
    // parse using safeParse it first
    const result = ContactFormSchema.safeParse(data);

    if (!result.success) {
      return { success: false, error: result.error };
    }

    const { companyEmail, content } = result.data;

    const response = await resendClient.emails.send({
      from: "Rathon <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: "New Contact Message from Rathon Website",
      react: EmailTemplate({ companyEmail, content }) as React.ReactElement,
      replyTo: companyEmail,
      tags: [{ name: "source", value: "website_contact" }],
    });

    if (response.error) {
      return { success: false, error: response.error };
    }

    return { success: true };
  } catch (err) {
    return { success: false, error: err };
  }
}
