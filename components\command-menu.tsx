'use client';

import type { DialogProps } from '@radix-ui/react-dialog';
import { allBlogs } from 'content-collections';
import {
  FeatherIcon,
  FileIcon,
  Laptop,
  Moon,
  PhoneIcon,
  Search,
  Sun,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';
import React from 'react';
import { Button } from '@/components/ui/button';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import { useIsMac } from '@/hooks/use-is-mac';
import { navItems } from '@/lib/docs';
import { cn } from '@/lib/utils';
import { TextShimmer } from './ui/text-shimmer';

const placeholders = ['Search blogs...', 'Find pages...', 'Toogle theme...'];

export function CommandMenu({ ...props }: DialogProps) {
  const router = useRouter();
  const isMac = useIsMac();
  const [open, setOpen] = React.useState(false);
  const { setTheme } = useTheme();
  const [placeholderIndex, setPlaceholderIndex] = React.useState(0);

  // Cycle placeholder every 2 seconds
  React.useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prev) => (prev + 1) % placeholders.length);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if ((e.key === 'k' && (e.metaKey || e.ctrlKey)) || e.key === '/') {
        if (
          (e.target instanceof HTMLElement && e.target.isContentEditable) ||
          e.target instanceof HTMLInputElement ||
          e.target instanceof HTMLTextAreaElement ||
          e.target instanceof HTMLSelectElement
        ) {
          return;
        }

        e.preventDefault();
        setOpen((op) => !op);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  const runCommand = React.useCallback((command: () => unknown) => {
    setOpen(false);
    command();
  }, []);

  return (
    <>
      <Button
        className={cn(
          'relative h-8 w-full cursor-pointer justify-start rounded-[0.5rem] border-none bg-muted/50 font-normal text-muted-foreground text-sm shadow-none hover:text-muted-foreground sm:pr-12 md:w-40 lg:w-56 xl:w-64'
        )}
        onClick={() => setOpen(true)}
        variant="outline"
        {...props}
      >
        <Search className="size-4" />
        <TextShimmer className="hidden lg:inline-flex" duration={1}>
          Search pages...
        </TextShimmer>
        <TextShimmer className="inline-flex lg:hidden" duration={1}>
          Search...
        </TextShimmer>

        <div className="absolute top-1.5 right-1.5 hidden gap-1 sm:flex">
          <CommandMenuKbd>{isMac ? '⌘' : 'Ctrl'}</CommandMenuKbd>
          <CommandMenuKbd className="aspect-square">K</CommandMenuKbd>
        </div>
      </Button>
      <CommandDialog
        className="rounded-xl border-none bg-clip-padding p-2 shadow-2xl ring-4 ring-neutral-200/80 dark:bg-neutral-900 dark:ring-neutral-800"
        onOpenChange={setOpen}
        open={open}
      >
        <CommandInput placeholder={placeholders[placeholderIndex]} />

        <CommandList>
          <CommandEmpty>No results found.</CommandEmpty>
          <CommandGroup heading="Pages">
            {navItems.map((item) => (
              <CommandItem
                key={item.label}
                onSelect={() => {
                  runCommand(() => router.push(`${item.href}`));
                }}
                value={item.label}
              >
                {item.icon ? <item.icon /> : <FileIcon />}

                {item.label}
              </CommandItem>
            ))}
            <CommandItem
              key="Book a Call"
              onSelect={() => {
                runCommand(() => router.push('/book'));
              }}
              value="Book a Call"
            >
              <PhoneIcon />
              Book a Call
            </CommandItem>
          </CommandGroup>
          <CommandGroup heading="Articles">
            {allBlogs.map((blog) => (
              <CommandItem
                className="line-clamp-1 flex items-center gap-2 truncate"
                key={blog._meta.path}
                onSelect={() => {
                  runCommand(() => router.push(`/blogs/${blog._meta.path}`));
                }}
                value={blog.title}
              >
                <FeatherIcon />
                {blog.title}
              </CommandItem>
            ))}
          </CommandGroup>

          <CommandSeparator />
          <CommandGroup heading="Theme">
            <CommandItem onSelect={() => runCommand(() => setTheme('light'))}>
              <Sun />
              Light
            </CommandItem>
            <CommandItem onSelect={() => runCommand(() => setTheme('dark'))}>
              <Moon />
              Dark
            </CommandItem>
            <CommandItem onSelect={() => runCommand(() => setTheme('system'))}>
              <Laptop />
              System
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}

function CommandMenuKbd({ className, ...props }: React.ComponentProps<'kbd'>) {
  return (
    <kbd
      className={cn(
        "pointer-events-none flex h-5 select-none items-center justify-center gap-1 rounded border bg-background px-1 font-medium font-sans text-[0.7rem] text-muted-foreground [&_svg:not([class*='size-'])]:size-3",
        className
      )}
      {...props}
    />
  );
}
