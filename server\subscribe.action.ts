'use server';

import { Resend } from 'resend';
import { SubscribedTemplate } from '@/components/subscribe-template';
import type { TSubFormSchema } from './types';

// Initialize Resend client with API key from environment variables
const resendClient = new Resend(process.env.RESEND_API_KEY);

export async function subscribe(formData: TSubFormSchema) {
  try {
    const { email } = formData;

    const response = await resendClient.emails.send({
      from: 'Rathon <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: 'New Subscriber from Rathon Website',
      react: SubscribedTemplate({ email }) as React.ReactElement,
      replyTo: email,
      tags: [{ name: 'source', value: 'website_subscribe' }],
    });
    // then send email to subscribe thanking him
    //  await resendClient.emails.send({
    //   from: "Rathon <<EMAIL>>",
    //   to: [email],
    //   subject: "Thank you for subscribing to <PERSON><PERSON>!",
    //   react: ThankYouForSubscribingTemplate({ email }) as React.ReactElement,
    //   replyTo: "<EMAIL>",
    //   tags: [{ name: "source", value: "website_subscribe" }],
    // });

    if (response.error) {
      return { success: false, error: response.error };
    }

    return { success: true };
  } catch (error) {
    return { success: false, error };
  }
}
