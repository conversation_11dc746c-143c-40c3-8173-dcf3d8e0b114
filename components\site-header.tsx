import { LibraryBigIcon } from 'lucide-react';
import Link from 'next/link';
import { siteConfig } from '@/lib/config';
import { cn } from '@/lib/utils';
import { CommandMenu } from './command-menu';
import { MainNav } from './main-nav';
import { MobileNav } from './mobile-nav';
import { Button, buttonVariants } from './ui/button';
import { ModeSwitcher } from './ui/mode-switcher';
import { Separator } from './ui/separator';

export function SiteHeader() {
  return (
    <header className="sticky top-0 z-50 w-full bg-background/40 backdrop-blur-lg supports-backdrop-blur:bg-background/90">
      <div className="container-wrapper 3xl:fixed:px-0 px-4 md:px-6">
        <div className="**:data-[slot=separator]:!h-4 3xl:fixed:container flex min-h-14 items-center gap-2">
          <MobileNav className="flex lg:hidden" />
          <Button
            asChild
            className="hidden size-8 lg:flex"
            size="icon"
            variant="ghost"
          >
            <Link href="/">
              <LibraryBigIcon className="size-5" />
              <span className="sr-only">{siteConfig.name}</span>
            </Link>
          </Button>
          <MainNav className="hidden lg:flex" />
          <div className="ml-auto flex items-center gap-2 md:flex-1 md:justify-end">
            <div className="hidden w-full flex-1 md:flex md:w-auto md:flex-none">
              <CommandMenu />
            </div>
            <Separator
              className="ml-2 hidden lg:block"
              orientation="vertical"
            />
            <Link
              href={siteConfig.links.twitter}
              rel="noreferrer"
              target="_blank"
            >
              <div
                className={cn(
                  buttonVariants({
                    variant: 'ghost',
                  }),
                  'w-9 px-0'
                )}
              >
                <svg
                  className="size-5 fill-foreground"
                  height="24px"
                  viewBox="0 0 24 24"
                  width="24px"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Twitter</title>
                  <path d="M 2.3671875 3 L 9.4628906 13.140625 L 2.7402344 21 L 5.3808594 21 L 10.644531 14.830078 L 14.960938 21 L 21.871094 21 L 14.449219 10.375 L 20.740234 3 L 18.140625 3 L 13.271484 8.6875 L 9.2988281 3 L 2.3671875 3 z M 6.2070312 5 L 8.2558594 5 L 18.033203 19 L 16.001953 19 L 6.2070312 5 z" />
                </svg>
                <span className="sr-only">Twitter</span>
              </div>
            </Link>
            <Separator className="3xl:flex hidden" orientation="vertical" />
            <Separator orientation="vertical" />
            <ModeSwitcher />
          </div>
        </div>
      </div>
    </header>
  );
}
