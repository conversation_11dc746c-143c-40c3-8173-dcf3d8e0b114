import {
  ClockIcon,
  MailIcon,
  MapPinIcon,
  MessageCircleIcon,
  PhoneIcon,
} from 'lucide-react';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/features/home/<USER>';
import ContactForm from './contact-form';

export function ContactHeroSection() {
  return (
    <section className="border-grid">
      <PageHeader className=" lg:pb-8 ">
        <PageHeaderHeading className="font-mono text-3xl lg:text-4xl">
          Contact Us
        </PageHeaderHeading>
        <PageHeaderDescription className=" text-foreground/80 tracking-tight">
          We&apos;re here to help. If you have any questions, please don&apos;t
          hesitate to reach out.
        </PageHeaderDescription>

        <div className="mx-auto w-full max-w-6xl py-6 text-left">
          <Grids />
          <div className="grid w-full grid-cols-1 gap-y-12 lg:grid-cols-2">
            <ContactInfo />
            <ContactForm />
          </div>
          <Grids />
        </div>
      </PageHeader>
    </section>
  );
}

function Grids() {
  return (
    <div className="relative hidden divide-x divide-y divide-dashed border border-grid *:p-4 sm:grid-cols-10 lg:grid lg:grid-cols-20 ">
      {Array.from({ length: 20 }).map((_, i) => (
        // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
        <div className="h-14" key={i} />
      ))}
    </div>
  );
}

function ContactInfo() {
  return (
    <div className="flex max-w-[27rem] flex-col gap-5 p-2 md:p-5">
      <h3 className="mb-2 font-bold text-4xl text-foreground leading-[1.2] tracking-tighter">
        Talk to our Sales team.
      </h3>
      <div className="flex flex-col gap-5">
        <p className="text-muted-foreground tracking-tight">
          <span className="font-bold text-primary">
            <PhoneIcon className="mr-2 inline-block size-4" />
            Get a custom demo.
          </span>
          Discover the value of Rathon for your enterprise and explore our
          custom plans and pricing.
        </p>
        <p className="text-muted-foreground tracking-tight">
          <span className="font-bold text-primary">
            <ClockIcon className="mr-2 inline-block size-4" />
            Set up your Enterprise trial :{' '}
          </span>
          We&apos;re available anytime, reach out whenever you&apos;re ready to
          see how Rathon can elevate your web presence.
        </p>
        <p className="text-muted-foreground tracking-tight">
          <span className="font-bold text-primary">
            <MailIcon className="mr-2 inline-block size-4" />
            Email us:
          </span>{' '}
          <a className="ml-1 hover:underline" href="mailto:<EMAIL>">
            <EMAIL>
          </a>
        </p>
        <p className="text-muted-foreground tracking-tight">
          <span className="font-bold text-primary">
            <MapPinIcon className="mr-2 inline-block size-4" />
            Our Location:
          </span>{' '}
          Kigali, Rwanda
        </p>
        <p className="text-muted-foreground tracking-tight">
          <span className="font-bold text-primary">
            <MessageCircleIcon className="mr-2 inline-block size-4" />
            Whatsapp:
          </span>{' '}
          +250 788 888 888
        </p>
      </div>
    </div>
  );
}
