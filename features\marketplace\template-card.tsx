import { ExternalLinkIcon, LibraryBigIcon } from 'lucide-react';
import Image, { type StaticImageData } from 'next/image';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';

type TemplateCard = {
  template: {
    title: string;
    description: string;
    image: StaticImageData;
    href: string;
  };
};
export function TemplateCard({ template }: TemplateCard) {
  return (
    <Link href={template.href} rel="noopener noreferrer" target="_blank">
      <Card className="divide relative flex h-full flex-col gap-4 rounded-sm pt-0 shadow-xs">
        <CardHeader className={cn('p-3')}>
          <Image
            alt="image placeholder"
            className="aspect-video w-full rounded-sm object-cover"
            placeholder="blur"
            priority
            src={template.image}
          />
        </CardHeader>
        <CardContent className="flex flex-col">
          <CardTitle className="text-base leading-5 tracking-tight">
            <p>{template.title}</p>
          </CardTitle>
          <CardDescription>{template.description}</CardDescription>
        </CardContent>
        <CardFooter className="mt-auto flex items-center justify-between gap-2">
          <div className="flex items-center gap-3 text-muted-foreground text-sm">
            <span>by</span>
            <div className="flex items-center gap-1">
              <LibraryBigIcon className="size-4" />
              <span>Rathon</span>
            </div>
          </div>
          <div>
            <span className="sr-only">View Template</span>
            <ExternalLinkIcon className="size-5 text-muted-foreground hover:text-foreground" />
          </div>
        </CardFooter>
      </Card>
    </Link>
  );
}
