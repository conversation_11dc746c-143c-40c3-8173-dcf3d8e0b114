import { AspectRatio } from '@/components/ui/aspect-ratio';
import {
  ImageComparison,
  ImageComparisonImage,
  ImageComparisonSlider,
} from '@/components/ui/image-comparison';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from './page-header';
export function ImageComparisonSpring() {
  return (
    <section className="border-grid" id="pricing">
      <PageHeader className="py-16 pb-0 md:pb-0 lg:pb-0">
        <PageHeaderHeading className="font-mono text-3xl lg:text-4xl">
          Web Comparison
        </PageHeaderHeading>
        <PageHeaderDescription className=" text-foreground/80 tracking-tight">
          Compare our web design and development services to other companies.
        </PageHeaderDescription>

        <AspectRatio
          className="relative mx-auto mt-8 max-w-4xl"
          ratio={1280 / 720}
        >
          <ImageComparison
            className="aspect-[1280/720] w-full rounded-sm border border-muted"
            enableHover
            springOptions={{
              bounce: 0.3,
            }}
          >
            <ImageComparisonImage
              alt="Motion Primitives Dark"
              className="object-cover"
              position="left"
              src="/showcase/ab.png"
            />
            <ImageComparisonImage
              alt="Motion Primitives Light"
              className="aspect-[1280/720] object-cover"
              position="right"
              src="/showcase/adls.png"
            />
            <ImageComparisonSlider className="w-0.5 bg-white/30 backdrop-blur-xs" />
          </ImageComparison>
        </AspectRatio>
      </PageHeader>
    </section>
  );
}
