import { cn } from '@/lib/utils';
import { But<PERSON> } from './button';
import { GlowEffect } from './glow-effect';

export function GlowButton({
  className,
  mode = 'colorShift',
  scale = 1,
  ...props
}: React.ComponentProps<typeof Button> & {
  mode?: 'colorShift' | 'rotate' | 'pulse' | 'breathe' | 'flowHorizontal';
  scale?: number;
}) {
  return (
    <div className="relative">
      <GlowEffect
        blur="soft"
        colors={['#FF5733', '#33FF57', '#3357FF', '#F1C40F']}
        duration={3}
        mode={mode}
        scale={scale}
      />
      <Button
        className={cn(
          'relative z-10 bg-zinc-950 text-zinc-50 outline-1 outline-[#fff2f21f] transition-all hover:bg-zinc-900 focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50',
          className
        )}
        {...props}
      />
    </div>
  );
}
