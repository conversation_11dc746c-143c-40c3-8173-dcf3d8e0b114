'use client';
import { allBlogs } from 'content-collections';
import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { ScrollA<PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { PageHeader } from '../home/<USER>';
import { BlogCards } from './blog-cards';
import { NoBlogsFound } from './no-blogs';

interface BlogsNavProps extends React.HTMLAttributes<HTMLDivElement> {}

export default function BlogsListSection({
  className,
  ...props
}: BlogsNavProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTag, setSelectedTag] = useState<string | null>(null);

  // Get unique tags and filter out undefined
  const tags = Array.from(
    new Set(allBlogs.map((blog) => blog.tag).filter(Boolean))
  ).sort();

  // Filter blogs based on search and tag
  const filteredBlogs = allBlogs.filter((blog) => {
    const matchesSearch =
      blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      blog?.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesTag = !selectedTag || blog.tag === selectedTag;
    return matchesSearch && matchesTag;
  });
  return (
    <>
      <header className="container py-4 md:py-8">
        <div className="relative flex w-full flex-col justify-between gap-6 py-6 md:flex-row">
          <ScrollArea className="max-w-[600px] lg:max-w-none">
            <div className={cn('flex items-center', className)} {...props}>
              <button
                className={cn(
                  'flex h-9 cursor-pointer items-center justify-center text-nowrap rounded-full px-4 text-center font-medium text-muted-foreground text-sm transition-colors hover:text-primary',
                  selectedTag === null && 'bg-secondary'
                )}
                onClick={() => setSelectedTag(null)}
                type="button"
              >
                All
              </button>
              {tags.map((tag) => (
                <button
                  className={cn(
                    'flex h-9 cursor-pointer items-center justify-center text-nowrap rounded-full px-4 text-center font-medium text-muted-foreground text-sm transition-colors hover:text-primary',
                    selectedTag === tag && 'bg-secondary'
                  )}
                  key={tag}
                  onClick={() => tag && setSelectedTag(tag)}
                  type="button"
                >
                  {tag}
                </button>
              ))}
            </div>
            <ScrollBar className="invisible" orientation="horizontal" />
          </ScrollArea>
          <div className={cn('flex items-center gap-2', className)}>
            <Input
              className="md:w-[100px] lg:w-[300px]"
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search..."
              type="search"
              value={searchQuery}
            />
          </div>
        </div>
      </header>

      <PageHeader className="pb-20 text-left lg:pt-0">
        {filteredBlogs.length === 0 ? (
          <NoBlogsFound />
        ) : (
          <div>
            <BlogCards blogs={filteredBlogs} />
          </div>
        )}
      </PageHeader>
    </>
  );
}
