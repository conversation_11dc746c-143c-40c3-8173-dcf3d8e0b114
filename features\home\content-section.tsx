import Image from "next/image";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import pcImage from "@/public/homeCover.jpg";
import { PageHeader, PageHeaderDescription } from "./page-header";
export function ContentSection() {
  return (
    <section className="border-grid bg-muted">
      <PageHeader className="py-16">
        <div className="flex flex-col items-center justify-center gap-6 text-left md:flex-row md:gap-12">
          <div className="flex-1 rounded-sm">
            <Image
              alt="payments illustration light"
              className="aspect-video h-full w-full rounded-sm object-cover transition-all hover:grayscale-0 lg:grayscale-75"
              placeholder="blur"
              src={pcImage}
            />
          </div>

          <div className="relative flex flex-1 flex-col gap-4">
            <PageHeaderDescription className="text-foreground tracking-tight">
              Web Design & Development Solutions for Businesses.{" "}
              <span className="font-bold text-accent-foreground">
                We support your entire online presence
              </span>{" "}
              to fuel long-term growth.
            </PageHeaderDescription>
            <PageHeaderDescription className="text-foreground/90 tracking-tight">
              We craft high-performance websites that are not only visually
              striking, but also optimized for speed, user experience, and
              scalability.
            </PageHeaderDescription>

            <div className="pt-4">
              <blockquote>
                <PageHeaderDescription className="text-foreground/80 tracking-tight">
                  Building fast, user-friendly, and scalable websites isn&apos;t
                  just what we do. It's what we love. Backed by years of
                  experience in web design and development, we started this
                  agency to help businesses craft stunning digital experiences
                  that deliver real results.
                </PageHeaderDescription>

                <div className="mt-6 flex items-center gap-2">
                  <Avatar>
                    <AvatarImage
                      alt="Leo Constantin"
                      className="grayscale"
                      src="https://github.com/leconstantin.png"
                    />
                    <AvatarFallback>LC</AvatarFallback>
                  </Avatar>
                  <cite className="block font-medium font-mono">
                    Founder & Lead Developer
                  </cite>
                </div>
              </blockquote>
            </div>
          </div>
        </div>
      </PageHeader>
    </section>
  );
}
