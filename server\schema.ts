import z from "zod";

export const SubFormSchema = z.object({
  email: z
    .string()
    .trim()
    .min(5, { message: "Email must be at least 5 characters." })
    .email({ message: "Please enter a valid email address." }),
});

export const ContactFormSchema = z.object({
  companyEmail: z
    .string()
    .trim()
    .min(5, { message: "Email must be at least 5 characters." })
    .email({ message: "Please enter a valid email address." }),

  content: z
    .string()
    .trim()
    .min(10, { message: "Describe it in at least 10 characters." })
    .max(500, { message: "Your message is too long. Book a call instead." }),
});
