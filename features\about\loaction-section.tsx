import { MapPinIcon } from 'lucide-react';
import {
  <PERSON>Header,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/features/home/<USER>';

export function LocationSection() {
  return (
    <section className="border-grid">
      <PageHeader className="">
        <PageHeaderHeading className="font-mono text-3xl lg:text-4xl">
          Our Location
        </PageHeaderHeading>
        <PageHeaderDescription className=" text-foreground/80 tracking-tight">
          We are located in kimironko sector near kimironko market, (Kigali,
          Rwanda). We are open to clients from all over the world.
        </PageHeaderDescription>
        <div className="flex items-center gap-2 text-muted-foreground transition-colors duration-150 hover:text-primary">
          <a
            href="https://www.google.com/maps/place/Rwanda/@-1.940278,29.873147,7z/data=!3m1!4b1!4m6!3m5!1s0x19dca609f0000001:0x586f7de5a0fe2b2a!8m2!3  d-1.940278!4d29.873147!16zL20vMDJ1N3I?entry=ttu"
            rel="noreferrer"
            target="_blank"
          >
            View on Google Maps
          </a>
          <MapPinIcon className="size-4" />
        </div>
        <div className="relative mx-auto w-full max-w-5xl overflow-hidden rounded-sm">
          <iframe
            allowFullScreen
            className="aspect-video h-full w-full rounded-sm lg:min-h-[500px]"
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1993.7548139437863!2d30.125355674356914!3d-1.949238838713868!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x19dca70feb2e6ae9%3A0x42c9567e13445a57!2sKimironko%20Market!5e0!3m2!1sen!2srw!4v1734010282439!5m2!1sen!2srw"
            title="Rathon Location"
          />
        </div>
      </PageHeader>
    </section>
  );
}
