import type { Metadata } from 'next';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/features/home/<USER>';
import { terms } from '@/lib/docs';

export const metadata: Metadata = {
  title: 'Terms of Service',
};

export default function Terms() {
  return (
    <section className="border-grid">
      <PageHeader className="px-6 text-left">
        <div className="mx-auto flex max-w-5xl flex-col gap-6">
          <div className="flex flex-col gap-3 py-6">
            <PageHeaderHeading className="font-bold text-3xl">
              Terms of Service
            </PageHeaderHeading>
            <p className="text-muted-foreground text-sm">
              Last updated: June 5, 2025
            </p>

            <PageHeaderDescription className="max-w-5xl leading-relaxed tracking-tight">
              Welcome to Rathon! By using our website and services, you agree to
              the following terms. We&apos;ve kept the legalese to a minimum and
              written things in plain English, because we respect your time.
            </PageHeaderDescription>
          </div>

          <div className="flex flex-col gap-6">
            {terms.map((term, index) => (
              <div className="flex flex-col gap-3" key={term.title}>
                <PageHeaderHeading className="text-lg xl:text-xl xl:tracking-tight">
                  {index + 1}. {term.title}
                </PageHeaderHeading>
                <PageHeaderDescription className="max-w-5xl sm:text-base">
                  {term.content}
                </PageHeaderDescription>
              </div>
            ))}
          </div>

          <p className="leading-relaxed tracking-tight">
            Thanks for being here. We&apos;re glad you&apos;re part of the
            Rathon journey.
          </p>
        </div>
      </PageHeader>
    </section>
  );
}
