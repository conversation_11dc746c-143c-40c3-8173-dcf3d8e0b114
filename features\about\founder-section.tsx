import { ExternalLinkIcon } from 'lucide-react';
import Image from 'next/image';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/features/home/<USER>';
import founder from '@/public/founder.jpeg';

export function FounderSection() {
  return (
    <PageHeader className="py-20 text-left md:py-8 lg:py-0">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 md:px-6">
        <div className="flex flex-col gap-5 lg:gap-10">
          <PageHeaderHeading className="font-mono text-3xl lg:text-4xl">
            Founder
          </PageHeaderHeading>
          <PageHeaderDescription className=" text-foreground/80 tracking-tight">
            <PERSON> is the founder and lead developer of Rathon. He has
            over 2 years of experience in web development and design. He is
            passionate about building beautiful, functional, and accessible
            websites.
          </PageHeaderDescription>
          <PageHeaderDescription className=" text-foreground/80 tracking-tight">
            I started <PERSON>hon because I kept seeing the same problem over and
            over websites that looked bad, didn&apos;t work well, and drove
            customers away. Slow load times, confusing layouts, and zero
            personality were costing businesses more than they realized
            sometimes even millions.
          </PageHeaderDescription>
          <PageHeaderDescription className=" text-foreground/80 tracking-tight">
            I knew it didn&apos;t have to be that way. A great website should
            feel fast, clear, and trustworthy. That&apos;s what we build here at
            Rathon, sites that not only look great but actually help you grow.
          </PageHeaderDescription>
          <div className="flex items-center gap-2 text-muted-foreground transition-colors duration-150 hover:text-primary">
            <a
              href="https://leo-constantin.vercel.app"
              rel="noreferrer"
              target="_blank"
            >
              View Leo&apos;s Portifolio
            </a>
            <ExternalLinkIcon className="size-4" />
          </div>
        </div>
        <div className="relative flex">
          <AspectRatio className="ml-auto w-full max-w-[450px]" ratio={1 / 1}>
            <Image
              alt="Image"
              className="rounded-sm object-cover grayscale"
              placeholder="blur"
              src={founder}
            />
          </AspectRatio>
        </div>
      </div>
    </PageHeader>
  );
}
