import {
  Body,
  Column,
  Container,
  Font,
  Head,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

interface EmailTemplateProps {
  companyEmail: string;
  content: string;
}

export const EmailTemplate = ({
  companyEmail,
  content,
}: EmailTemplateProps) => {
  const currentDate = new Date().toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });

  const safeEmail = companyEmail || '[No email provided]';

  return (
    <Html>
      <Head>
        <Font
          fallbackFontFamily="Arial"
          fontFamily="Roboto"
          fontStyle="normal"
          fontWeight={400}
          webFont={{
            url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
            format: 'woff2',
          }}
        />
      </Head>
      <Preview>New contact form message from {safeEmail}</Preview>
      <Tailwind>
        <Body className="mx-auto my-auto bg-gray-50 font-sans">
          <Container className="mx-auto my-8 max-w-[600px]">
            {/* Header */}
            <Section className="rounded-t-lg bg-blue-600 px-8 py-6">
              <Row>
                <Column>
                  <Text className="text-center font-bold text-3xl text-white">
                    Rathon
                  </Text>
                </Column>
              </Row>
            </Section>

            {/* Main Content */}
            <Section className="rounded-b-lg bg-white px-8 py-10 shadow-sm">
              <Row>
                <Column>
                  <Text className="mb-5 font-bold text-2xl text-gray-800">
                    New Contact Form Submission
                  </Text>

                  <Text className="mb-4 text-gray-700">
                    You’ve received a new message via the Rathon website contact
                    form.
                  </Text>

                  <Container className="mb-6 rounded-md border-blue-500 border-l-4 bg-blue-50 p-5">
                    <Text className="mb-1 font-medium text-blue-800">
                      <strong>From:</strong> {safeEmail}
                    </Text>

                    <Text className="mb-2 font-medium text-blue-800">
                      <strong>Message:</strong>
                    </Text>

                    <Container className="rounded-md border border-gray-200 bg-white p-4">
                      <Text className="whitespace-pre-line text-gray-800 text-sm leading-relaxed">
                        {content}
                      </Text>
                    </Container>

                    <Text className="mt-4 text-blue-800">
                      <strong>Sent on:</strong> {currentDate}
                    </Text>
                  </Container>

                  <Hr className="my-6 border-gray-200" />

                  <Text className="mb-1 text-gray-700 text-sm">
                    Please reply directly to <strong>{safeEmail}</strong> to
                    follow up with this inquiry.
                  </Text>
                </Column>
              </Row>
            </Section>

            {/* Footer */}
            <Section className="px-8 py-6">
              <Text className="text-center text-gray-500 text-xs">
                © {new Date().getFullYear()} Rathon. All rights reserved.
              </Text>
              <Text className="text-center text-gray-500 text-xs">
                This message was generated from the contact form on your
                website.
              </Text>
              <Text className="text-center text-gray-500 text-xs">
                <Link className="text-blue-500 underline" href="#">
                  Manage contact notifications
                </Link>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};
