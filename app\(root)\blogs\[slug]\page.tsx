import { allBlogs } from 'content-collections';
import { ChevronLeftIcon } from 'lucide-react';
import type { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ScrollProgress } from '@/components/ui/scroll-progress';
import CopyButton from '@/features/blogs/copy-btn';
import MoreBlogsSection, {
  getReadingTime,
} from '@/features/blogs/more-blogs-section';
import PromotionCard from '@/features/blogs/promotion-card';
import BlogTableOfContents from '@/features/blogs/table-of-content';
import { PageHeader } from '@/features/home/<USER>';
import { siteConfig } from '@/lib/config';
import { BlogBody } from './_blog-body';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;
  const postSlug = slug;
  const post = allBlogs.find((p) => p._meta.path === postSlug);

  if (!post) {
    return {
      title: 'Blog Post Not Found',
      description: 'The requested blog post could not be found.',
    };
  }

  return {
    metadataBase: new URL(siteConfig.url),
    title: `${post.title} | ${siteConfig.name}`,
    description: post.description,
    openGraph: {
      title: post.title,
      description: post.description,
      type: 'article',
      images: [
        {
          url: post.image || '',
          width: 1200,
          height: 630,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.description,
      images: [post.image || ''],
      creator: '@rathonagency',
    },
  };
}
export async function generateStaticParams() {
  return await allBlogs.map((post) => ({
    slug: post.slugAsParams,
  }));
}
export default async function page({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const currentIndex = allBlogs.findIndex((pos) => pos.slugAsParams === slug);
  const post = allBlogs[currentIndex];

  // Initialize with empty headings array - they will be populated after render
  const headings: string[] = [];

  return (
    <section className="border-grid">
      <PageHeader className="relative text-left">
        <div className="relative mx-auto mt-5 h-full w-full max-w-6xl">
          <ScrollProgress className="top-[56px] h-[0.15rem]" />

          <div className="mb-4 flex w-full items-center justify-between">
            <Button asChild className="rounded-full" variant="secondary">
              <Link
                className="flex w-fit items-center justify-center text-secondary-foreground text-sm"
                href="/blogs"
              >
                <ChevronLeftIcon className="mr-2 size-4" />
                All Blogs
              </Link>
            </Button>
            <CopyButton
              text={post ? `${siteConfig.url}/blogs/${post._meta.path}` : ''}
            />
          </div>

          <article className="mt-5 rounded-sm md:border md:border-border">
            {post && (
              <>
                <div className="relative overflow-hidden p-5 md:p-10">
                  <Image
                    alt={post.title}
                    className="size-full rounded-sm border border-border object-cover object-left lg:h-[500px]"
                    height={500}
                    priority
                    src={post.image || '/computer-hand.jpg'}
                    width={500}
                  />
                </div>

                <div className="mx-auto flex flex-col items-center justify-center gap-y-2 border-border border-y py-5 md:p-5">
                  <div className="mx-auto flex max-w-4xl flex-col items-center justify-center gap-y-2">
                    <h1 className="text-balance text-center font-semibold text-3xl tracking-tighter md:text-5xl">
                      {post.title}
                    </h1>
                    <p className="hidden text-balance text-center text-secondary-foreground md:block md:text-lg">
                      {post.description}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-center gap-x-2 border-border border-b p-3 text-secondary-foreground text-sm">
                  <span>{getReadingTime(post.body.raw)} min read</span>
                  {post.tag && (
                    <>
                      <span>·</span>
                      <span className="rounded-full border border-border bg-primary/5 px-2.5 py-0.5">
                        {post.tag}
                      </span>
                    </>
                  )}
                </div>
              </>
            )}

            <div className="relative grid grid-cols-1 gap-x-1 lg:grid-cols-7">
              <BlogBody post={post} />

              <div className="sticky top-16 col-span-2 hidden h-fit w-full flex-col items-start justify-start p-5 text-primary lg:flex">
                <PromotionCard />
                <div className="mt-10 w-full">
                  <BlogTableOfContents headings={headings} />
                </div>
              </div>
            </div>
          </article>

          {post && <MoreBlogsSection currentPost={post} />}
        </div>
      </PageHeader>
    </section>
  );
}
