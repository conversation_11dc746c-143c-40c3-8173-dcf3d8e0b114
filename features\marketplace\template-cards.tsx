import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { TextShimmerWave } from '@/components/ui/text-shimmer-wave';
import { templates } from '@/lib/docs';
import { PageHeader, PageHeaderHeading } from '../home/<USER>';
import { TemplateCard } from './template-card';

export function TemplateCards() {
  return (
    <section className="border-grid">
      <PageHeader className="text-left">
        <div className="relative grid grid-cols-1 gap-8 gap-y-14 overflow-hidden md:grid-cols-2 md:px-4 lg:grid-cols-3">
          {templates.map((template) => (
            <TemplateCard key={template.title} template={template} />
          ))}
          <div className="flex flex-col items-center justify-center">
            <TextShimmerWave className="font-mono text-sm" duration={1}>
              More are comming soon, stay tuned!
            </TextShimmerWave>
          </div>
          <div className="flex flex-col gap-6">
            <PageHeaderHeading className="text-xl xl:text-2xl">
              Need a Custom Template ?
            </PageHeaderHeading>
            <Button asChild className="rounded-sm" size={'xlg'}>
              <Link href="/contact">Contact Us</Link>
            </Button>
          </div>
        </div>
      </PageHeader>
    </section>
  );
}
