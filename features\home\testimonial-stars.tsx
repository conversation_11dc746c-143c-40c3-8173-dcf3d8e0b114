'use client';
import { StarIcon } from 'lucide-react';
import { motion } from 'motion/react';
import { InView } from '@/components/ui/in-view';

export function TestimonialStars({ stars }: { stars: number }) {
  return (
    <InView
      variants={{
        hidden: {
          opacity: 0,
        },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: 0.09,
          },
        },
      }}
      viewOptions={{ once: false, margin: '0px 0px -250px 0px' }}
    >
      <div className="flex items-center gap-2">
        {Array.from({ length: stars }).map((_, index) => (
          <motion.div
            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            key={index}
            variants={{
              hidden: { opacity: 0, scale: 0.8, filter: 'blur(10px)' },
              visible: {
                opacity: 1,
                scale: 1,
                filter: 'blur(0px)',
              },
            }}
          >
            <StarIcon className="size-4 fill-yellow-500 text-yellow-500" />
          </motion.div>
        ))}
      </div>
    </InView>
  );
}
