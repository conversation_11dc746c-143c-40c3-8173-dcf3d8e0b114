import type { Blog } from 'content-collections';
import { allBlogs } from 'content-collections';
import { ArrowRightIcon } from 'lucide-react';
import { BlogCard } from './blog-card';

const WORD_SPLIT_REGEX = /\s+/;

export function getReadingTime(text: string): number {
  const wordsPerMinute = 200;
  const words = text.split(WORD_SPLIT_REGEX).length;
  return Math.ceil(words / wordsPerMinute);
}

function getRelatedPosts(currentPost: Blog, limit = 3) {
  // Filter out the current post and only include posts with the same tag
  return allBlogs
    .filter(
      (post) =>
        post.tag === currentPost.tag &&
        post._meta.path !== currentPost._meta.path
    )
    .slice(0, limit);
}

export default function MoreBlogsSection({
  currentPost,
}: {
  currentPost: Blog;
}) {
  const relatedPosts = getRelatedPosts(currentPost);

  // If no related posts are found, return null or an alternative component
  if (relatedPosts.length === 0) {
    return null;
  }

  return (
    <section className="mx-auto max-w-6xl py-10">
      <h2 className="mb-5 flex items-center gap-2 font-medium text-xl tracking-tighter">
        Read more like this
        <ArrowRightIcon className="size-4" />
      </h2>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {relatedPosts.map((post) => (
          <BlogCard blog={post} isMore key={post._meta.path} />
        ))}
      </div>
    </section>
  );
}
